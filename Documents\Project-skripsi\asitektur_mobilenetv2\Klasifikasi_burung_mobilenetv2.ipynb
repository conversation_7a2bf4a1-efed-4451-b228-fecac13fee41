import tensorflow as tf
from google.colab import drive
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.utils.class_weight import compute_class_weight
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix


input_shape = (224, 224, 3)
num_classes = 4

datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    fill_mode='nearest',
    validation_split=0.3,
)


#input data
drive.mount('/content/drive')
main_data_dir = '/content/drive/MyDrive/images'
!ls '/content/drive/MyDrive/images'
# Set batch size
batch_size = 128

# Define train and test generators
train_generator = datagen.flow_from_directory(
    main_data_dir,
    target_size=(224, 224),
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=True,
    subset="training"
)

valid_generator = datagen.flow_from_directory(
    main_data_dir,
    target_size=(224, 224),
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=False,
    subset="validation"
)


base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)

for layer in base_model.layers:
    layer.trainable = False

x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dropout(0.5)(x)
x = Dense(1024, activation='relu')(x)
x = Dropout(0.5)(x)
predictions = Dense(num_classes, activation='softmax')(x)

# Combine base model and custom head
model = Model(inputs=base_model.input, outputs=predictions)

# Fine-tune the model by unfreezing some layers
for layer in base_model.layers[:-10]:
    layer.trainable = False

# Implement learning rate scheduling
lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
    initial_learning_rate=1e-4,
    decay_steps=1000,
    decay_rate=0.9
)
optimizer = Adam(learning_rate=lr_schedule)


model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])


# Implement early stopping callback
early_stopping = tf.keras.callbacks.EarlyStopping(
    monitor='val_loss',
    patience=3,
    restore_best_weights=True
)

# Train the model with modified settings
history = model.fit(
    train_generator,
    epochs=30,  # Increase epochs for better training
    validation_data=valid_generator,
    callbacks=[early_stopping],
    verbose=1
)

test_loss, test_accuracy = model.evaluate(valid_generator)
print("Test Loss:", test_loss)
print("Test Accuracy:", test_accuracy)

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.metrics import confusion_matrix
# Define the path to the validation directory
validation_dir = '/content/drive/MyDrive/data_validation'

# Define the data generators
validation_datagen = ImageDataGenerator(rescale=1./255)

# Load the validation data using the ImageDataGenerator
validation_generator = validation_datagen.flow_from_directory(
    validation_dir,
    target_size=(224, 224),
    batch_size=batch_size,
    class_mode='categorical',
    shuffle=False
)

# Get the number of classes
num_classes = len(validation_generator.class_indices)

# Define the class labels
class_labels = list(validation_generator.class_indices.keys())

# Make predictions on the validation data
validation_predictions = model.predict(validation_generator)
predicted_labels_validation = np.argmax(validation_predictions, axis=1)

# Get the true labels for the validation data
true_labels_validation = validation_generator.classes

# Calculate confusion matrix for validation data
conf_matrix_validation = confusion_matrix(true_labels_validation, predicted_labels_validation)

# Plot the confusion matrix
plt.figure(figsize=(15, 10))
sns.heatmap(conf_matrix_validation, annot=True, fmt='d', xticklabels=class_labels, yticklabels=class_labels, cmap='Reds')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title('Confusion Matrix')
plt.xticks(rotation=90)
plt.show()


model.save('birds_classification.h5')

from IPython.display import FileLink
FileLink(r'birds_classification.h5')